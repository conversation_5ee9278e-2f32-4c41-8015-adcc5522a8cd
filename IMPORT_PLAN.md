# Garador Products Import Plan

## Current Status
- **CSV File**: `File (4).csv` with 407 products ✅
- **Existing Products**: 2 already imported (parts 233, 7333) ✅
- **Products to Import**: 405 remaining products
- **Shopify MCP Status**: ❌ Has GraphQL schema compatibility issues

## Import Strategy

### Phase 1: Fix Existing Products (Manual)
The 2 existing products need SKUs added:
1. **Product 233**: "Handle pack for steel up & over doors with canopy gear (steel framed) (C2)"
   - Add SKU: "233"
2. **Product 7333**: "Anthracite Grey (RAL 7016) Aerosol Spray Paint" 
   - Add SKU: "7333"

### Phase 2: Create Collections
Create these Shopify collections for product organization:

1. **Canopy Doors** - Canopy up & over garage door parts
2. **Retractable Doors** - Retractable garage door components  
3. **Retractable Plus Doors** - Enhanced retractable door parts
4. **Double Doors** - Double garage door components
5. **Sectional Doors** - Sectional garage door parts
6. **Side Doors** - Side hinged garage door parts
7. **Roller Doors** - Roller garage door components
8. **GaraMatic Operators** - Electric garage door operators
9. **EuroPro** - EuroPro electric operator parts
10. **Batteries** - Replacement batteries
11. **Bulbs** - Replacement bulbs
12. **Touch Up Paint** - Paint for maintenance

### Phase 3: Product Import Options

#### Option A: Shopify CSV Import (Recommended)
1. Convert the Garador CSV to Shopify CSV format
2. Use Shopify Admin's built-in CSV import feature
3. Handles duplicates automatically via SKU matching

#### Option B: Shopify API/App
1. Use a third-party import app like Matrixify
2. Or develop custom Shopify app with proper API access
3. Batch import with error handling

#### Option C: Manual Import (Small batches)
1. Import 20-50 products at a time manually
2. Use the product data prepared in our scripts
3. Ensure SKU consistency for duplicate prevention

## Shopify CSV Format

Here's the required CSV structure for Shopify import:

```csv
Handle,Title,Body (HTML),Vendor,Product Type,Tags,Published,Option1 Name,Option1 Value,Variant SKU,Variant Grams,Variant Inventory Tracker,Variant Inventory Qty,Variant Inventory Policy,Variant Fulfillment Service,Variant Price,Variant Compare At Price,Variant Requires Shipping,Variant Taxable,Variant Barcode,Image Src,Image Position,Image Alt Text,Gift Card,SEO Title,SEO Description,Google Shopping / Google Product Category,Google Shopping / Gender,Google Shopping / Age Group,Google Shopping / MPN,Google Shopping / AdWords Grouping,Google Shopping / AdWords Labels,Google Shopping / Condition,Google Shopping / Custom Product,Google Shopping / Custom Label 0,Google Shopping / Custom Label 1,Google Shopping / Custom Label 2,Google Shopping / Custom Label 3,Google Shopping / Custom Label 4,Variant Image,Variant Weight Unit,Collection
```

## Data Mapping

| Garador CSV | Shopify CSV | Notes |
|-------------|-------------|-------|
| partNumber | Variant SKU | Primary duplicate detection |
| title | Title | Product name |
| description | Body (HTML) | Add part number prefix |
| priceIncVat | Variant Price | Already in GBP |
| largeImageUrl | Image Src | High resolution image |
| category | Collection | Map to collection names |
| department | Product Type | Product categorization |

## Duplicate Prevention Strategy

1. **Primary**: SKU-based matching (partNumber from CSV)
2. **Secondary**: Title comparison for existing products
3. **Verification**: Check product count before/after import

## Quality Assurance Checklist

- [ ] All 407 products have unique part numbers
- [ ] All prices are valid (> 0)
- [ ] All image URLs are accessible
- [ ] Product titles are clean (no HTML entities)
- [ ] Collections are created and mapped correctly
- [ ] SKUs are set for duplicate detection
- [ ] SEO titles and descriptions are optimized

## Next Steps

1. **Immediate**: Create Shopify CSV from Garador CSV
2. **Test**: Import 5-10 products manually to validate process
3. **Execute**: Full import via chosen method
4. **Verify**: Check all products imported correctly
5. **Optimize**: Set up collections, SEO, and product organization

## Files Created

- `garador_import.py` - Python import script (needs working Shopify API)
- `shopify_import_helper.py` - CSV processing utilities
- `IMPORT_PLAN.md` - This comprehensive plan

## Risk Mitigation

- **Backup**: Export current Shopify products before import
- **Testing**: Start with small batch (5-10 products)
- **Monitoring**: Check for errors during import process
- **Rollback**: Have plan to remove imported products if needed
