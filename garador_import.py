#!/usr/bin/env python3
"""
Garador Products Import Script for Shopify
Imports products from CSV while avoiding duplicates
"""

import csv
import json
import re
from typing import Dict, List, Set, Optional

class GaradorImporter:
    def __init__(self):
        self.csv_file = "File (4).csv"
        self.existing_products = []
        self.existing_part_numbers = set()
        self.collections_map = {}
        self.import_stats = {
            "total_in_csv": 0,
            "already_exists": 0,
            "imported": 0,
            "errors": 0
        }
    
    def extract_part_number_from_description(self, description: str) -> Optional[str]:
        """Extract part number from product description"""
        match = re.search(r'Part number:\s*(\w+)', description)
        return match.group(1) if match else None
    
    def load_existing_products(self):
        """Load all existing products from Shopify to check for duplicates"""
        print("Loading existing products from Shopify...")
        # This would use the Shopify MCP to get all products
        # For now, we know there are 2 products: 233 and 7333
        self.existing_part_numbers = {"233", "7333"}
        print(f"Found {len(self.existing_part_numbers)} existing products")
    
    def create_collections(self):
        """Create Shopify collections for product categories"""
        print("Creating product collections...")
        
        # Define collection mapping from CSV categories
        collections_to_create = {
            "Canopy Doors": "Canopy Doors - Garage door parts for canopy up & over doors",
            "Retractable Doors": "Retractable Doors - Parts for retractable garage doors", 
            "Retractable Plus Doors": "Retractable Plus Doors - Enhanced retractable door components",
            "Double Doors": "Double Doors - Parts for double garage doors",
            "Sectional Doors": "Sectional Doors - Components for sectional garage doors",
            "Side Doors": "Side Doors - Side hinged garage door parts",
            "Roller Doors": "Roller Doors - Roller garage door components",
            "GaraMatic Operators": "GaraMatic Operators - Electric garage door operators",
            "EuroPro": "EuroPro - Electric operator components",
            "Batteries": "Batteries - Replacement batteries for operators",
            "Bulbs": "Bulbs - Replacement bulbs for garage door operators", 
            "Touch Up Paint": "Touch Up Paint - Paint for garage door maintenance"
        }
        
        for collection_name, description in collections_to_create.items():
            # This would use Shopify MCP to create collections
            print(f"Creating collection: {collection_name}")
            self.collections_map[collection_name] = f"collection_id_{collection_name.lower().replace(' ', '_')}"
    
    def process_csv_row(self, row: Dict) -> Dict:
        """Process a single CSV row into Shopify product format"""
        part_number = row['partNumber']
        
        # Check if already exists
        if part_number in self.existing_part_numbers:
            self.import_stats["already_exists"] += 1
            return None
        
        # Determine collection based on category
        category = row['category']
        collection_id = self.collections_map.get(category)
        
        # Create product data structure
        product_data = {
            "title": row['title'],
            "description": row['description'],
            "vendor": "Garador",
            "productType": row['department'],
            "status": "ACTIVE",
            "variants": [{
                "price": str(float(row['priceIncVat'])),
                "sku": part_number,
                "inventoryManagement": "NOT_MANAGED",
                "taxable": True,
                "options": ["Default Title"]
            }],
            "images": [{
                "src": row['largeImageUrl'],
                "altText": row['title']
            }],
            "tags": [row['department'], row['category'], "Garador", "Spare Parts"],
            "seo": {
                "title": row['title'],
                "description": f"Genuine Garador spare part {part_number}. {row['description']}"
            }
        }
        
        if collection_id:
            product_data["collectionsToJoin"] = [collection_id]
        
        return product_data
    
    def import_products_batch(self, products_batch: List[Dict]) -> List[str]:
        """Import a batch of products to Shopify"""
        imported_ids = []
        
        for product_data in products_batch:
            if product_data is None:
                continue
                
            try:
                print(f"Importing: {product_data['title']} (SKU: {product_data['variants'][0]['sku']})")
                
                # This would use Shopify MCP create-product
                # For now, simulate the import
                product_id = f"gid://shopify/Product/{len(imported_ids) + 1000}"
                imported_ids.append(product_id)
                self.import_stats["imported"] += 1
                
            except Exception as e:
                print(f"Error importing {product_data['title']}: {e}")
                self.import_stats["errors"] += 1
        
        return imported_ids
    
    def run_import(self):
        """Main import process"""
        print("Starting Garador product import...")
        
        # Step 1: Load existing products
        self.load_existing_products()
        
        # Step 2: Create collections
        self.create_collections()
        
        # Step 3: Process CSV and import in batches
        batch_size = 20
        current_batch = []
        
        with open(self.csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                self.import_stats["total_in_csv"] += 1
                
                product_data = self.process_csv_row(row)
                if product_data:
                    current_batch.append(product_data)
                
                # Import batch when it reaches batch_size
                if len(current_batch) >= batch_size:
                    self.import_products_batch(current_batch)
                    current_batch = []
                    print(f"Progress: {self.import_stats['imported']} imported, {self.import_stats['already_exists']} skipped")
            
            # Import remaining products in final batch
            if current_batch:
                self.import_products_batch(current_batch)
        
        # Step 4: Print final statistics
        self.print_import_summary()
    
    def print_import_summary(self):
        """Print import statistics"""
        print("\n" + "="*50)
        print("IMPORT SUMMARY")
        print("="*50)
        print(f"Total products in CSV: {self.import_stats['total_in_csv']}")
        print(f"Already existed (skipped): {self.import_stats['already_exists']}")
        print(f"Successfully imported: {self.import_stats['imported']}")
        print(f"Errors: {self.import_stats['errors']}")
        print(f"Success rate: {(self.import_stats['imported'] / (self.import_stats['total_in_csv'] - self.import_stats['already_exists']) * 100):.1f}%")
        print("="*50)

if __name__ == "__main__":
    importer = GaradorImporter()
    importer.run_import()
