#!/usr/bin/env python3
"""
Create full Shopify CSV for import
"""

import csv

def create_shopify_csv():
    # Read Garador CSV
    products = []
    with open('File (4).csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['partNumber'] not in ['233', '7333']:  # Skip existing
                products.append(row)

    print(f'Converting {len(products)} products...')

    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Variant SKU', 'Variant Price', 
        'Variant Requires Shipping', 'Variant Taxable', 'Image Src', 'Image Alt Text',
        'SEO Title', 'SEO Description', 'Collection'
    ]

    # Convert to Shopify format
    with open('shopify_import_full.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        
        for product in products:
            # Clean HTML entities
            title = product['title'].replace('&amp;', '&').replace('&#39;', "'").replace('&nbsp;', ' ')
            description = product['description'].replace('&amp;', '&').replace('&#39;', "'").replace('&nbsp;', ' ')
            
            # Create handle (URL-friendly)
            handle = title.lower()
            handle = handle.replace(' ', '-').replace('&', 'and').replace('(', '').replace(')', '')
            handle = handle.replace('/', '-').replace(',', '').replace('"', '').replace("'", '')
            handle = handle.replace('--', '-').strip('-')[:100]  # Shopify handle limit
            
            shopify_row = {
                'Handle': handle,
                'Title': title,
                'Body (HTML)': f'<p><strong>Part Number:</strong> {product["partNumber"]}</p><p>{description}</p>',
                'Vendor': 'Garador',
                'Product Type': product['department'],
                'Tags': f'Garador, Spare Parts, {product["category"]}, Part-{product["partNumber"]}',
                'Published': 'TRUE',
                'Option1 Name': 'Title',
                'Option1 Value': 'Default Title',
                'Variant SKU': product['partNumber'],
                'Variant Price': product['priceIncVat'],
                'Variant Requires Shipping': 'TRUE',
                'Variant Taxable': 'TRUE',
                'Image Src': product['largeImageUrl'],
                'Image Alt Text': title,
                'SEO Title': f'{title} - Garador Part {product["partNumber"]}',
                'SEO Description': f'Genuine Garador spare part {product["partNumber"]}. {description[:120]}...',
                'Collection': product['category']
            }
            writer.writerow(shopify_row)

    print('Conversion complete!')
    print(f'File created: shopify_import_full.csv')
    print(f'Products converted: {len(products)}')
    return len(products)

if __name__ == "__main__":
    create_shopify_csv()
