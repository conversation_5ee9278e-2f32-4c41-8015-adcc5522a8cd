# PowerShell script to create 100-product batch for Shopify import

# Read the CSV
$csv = Import-Csv 'File (4).csv'

# Products already imported (update this list as we go)
# Original: 233, 7333
# Test batch: 228, 59, 60, 187, 160161
# Batch 1 (10 products): 160162, 160163, 160164, 160165, 160166, 160167, 160168, 160169, 160170, 232
# Batch 2 (100 products): Need to get the actual SKUs from the CSV we created
$skipSkus = @('233', '7333', '228', '59', '60', '187', '160161', '160162', '160163', '160164', '160165', '160166', '160167', '160168', '160169', '160170', '232')

# Add the 100 products from the previous batch by reading the CSV
if (Test-Path 'shopify_batch_100.csv') {
    $previousBatch = Import-Csv 'shopify_batch_100.csv'
    $previousSkus = $previousBatch.'Variant SKU'
    $skipSkus += $previousSkus
    Write-Host "Added $($previousSkus.Count) SKUs from previous batch"
}

# Filter out already imported products and take next 100
$productsToImport = $csv | Where-Object { $_.partNumber -notin $skipSkus } | Select-Object -First 100

Write-Host "Creating batch with $($productsToImport.Count) products..."

# Create Shopify format array
$shopifyProducts = @()

foreach ($product in $productsToImport) {
    # Clean HTML entities
    $title = $product.title -replace '&amp;', '&' -replace '&#39;', "'" -replace '&nbsp;', ' ' -replace '&quot;', '"'
    $description = $product.description -replace '&amp;', '&' -replace '&#39;', "'" -replace '&nbsp;', ' ' -replace '&quot;', '"'
    
    # Create URL-friendly handle
    $handle = $title.ToLower()
    $handle = $handle -replace ' ', '-' -replace '&', 'and' -replace '[()\/,\"'']', '' -replace '--+', '-'
    $handle = $handle.Trim('-')
    if ($handle.Length -gt 100) { $handle = $handle.Substring(0, 100) }
    
    # Create Shopify product object
    $shopifyProduct = [PSCustomObject]@{
        'Handle' = $handle
        'Title' = $title
        'Body (HTML)' = "<p><strong>Part Number:</strong> $($product.partNumber)</p><p>$description</p>"
        'Vendor' = 'Garador'
        'Product Type' = $product.department
        'Tags' = "Garador, Spare Parts, $($product.category), Part-$($product.partNumber)"
        'Published' = 'TRUE'
        'Option1 Name' = 'Title'
        'Option1 Value' = 'Default Title'
        'Variant SKU' = $product.partNumber
        'Variant Price' = $product.priceIncVat
        'Variant Requires Shipping' = 'TRUE'
        'Variant Taxable' = 'TRUE'
        'Image Src' = $product.largeImageUrl
        'Image Alt Text' = $title
        'SEO Title' = "$title - Garador Part $($product.partNumber)"
        'SEO Description' = "Genuine Garador spare part $($product.partNumber). $($description.Substring(0, [Math]::Min(120, $description.Length)))..."
        'Collection' = $product.category
    }
    $shopifyProducts += $shopifyProduct
}

# Export to CSV
$shopifyProducts | Export-Csv -Path 'shopify_batch_200.csv' -NoTypeInformation -Encoding UTF8

Write-Host "Created shopify_batch_200.csv with $($shopifyProducts.Count) products"

# Show breakdown by category
$categoryBreakdown = $shopifyProducts | Group-Object Collection | Sort-Object Name
Write-Host "`nProducts by category:"
foreach ($group in $categoryBreakdown) {
    Write-Host "  $($group.Name): $($group.Count) products"
}

# Show first few product SKUs for verification
Write-Host "`nFirst 10 product SKUs in this batch:"
$shopifyProducts | Select-Object -First 10 | ForEach-Object { Write-Host "  $($_.'Variant SKU') - $($_.'Title')" }
