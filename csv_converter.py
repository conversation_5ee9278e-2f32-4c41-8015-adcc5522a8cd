#!/usr/bin/env python3
"""
Convert Garador CSV to Shopify CSV format
"""

import csv
import html
import re

def clean_html_entities(text):
    """Clean HTML entities from text"""
    if not text:
        return ""
    # Replace common HTML entities
    text = text.replace('&amp;', '&')
    text = text.replace('&nbsp;', ' ')
    text = text.replace('&#39;', "'")
    text = text.replace('&quot;', '"')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    return text.strip()

def create_handle(title):
    """Create Shopify handle from title"""
    handle = title.lower()
    handle = re.sub(r'[^a-z0-9\s-]', '', handle)
    handle = re.sub(r'\s+', '-', handle)
    handle = re.sub(r'-+', '-', handle)
    return handle.strip('-')

def map_category_to_collection(category):
    """Map Garador category to Shopify collection"""
    category_mapping = {
        "Canopy Doors": "Canopy Doors",
        "Retractable Doors": "Retractable Doors", 
        "Retractable Plus Doors": "Retractable Plus Doors",
        "Double Doors": "Double Doors",
        "Sectional Doors": "Sectional Doors",
        "Side Doors": "Side Doors",
        "Roller Doors": "Roller Doors",
        "GaraMatic Operators": "GaraMatic Operators",
        "EuroPro": "EuroPro",
        "Batteries": "Batteries",
        "Bulbs": "Bulbs",
        "Touch Up Paint": "Touch Up Paint"
    }
    return category_mapping.get(category, category)

def convert_garador_to_shopify_csv(input_file="File (4).csv", output_file="shopify_import_full.csv"):
    """Convert Garador CSV to Shopify CSV format"""
    
    # Shopify CSV headers
    shopify_headers = [
        "Handle", "Title", "Body (HTML)", "Vendor", "Product Type", "Tags", "Published",
        "Option1 Name", "Option1 Value", "Variant SKU", "Variant Grams", 
        "Variant Inventory Tracker", "Variant Inventory Qty", "Variant Inventory Policy",
        "Variant Fulfillment Service", "Variant Price", "Variant Compare At Price",
        "Variant Requires Shipping", "Variant Taxable", "Variant Barcode",
        "Image Src", "Image Position", "Image Alt Text", "Gift Card",
        "SEO Title", "SEO Description", "Collection"
    ]
    
    converted_products = []
    existing_skus = {"233", "7333"}  # Skip already imported products
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        
        for row in reader:
            part_number = row['partNumber'].strip()
            
            # Skip if already exists
            if part_number in existing_skus:
                print(f"Skipping existing product: {part_number}")
                continue
            
            # Clean data
            title = clean_html_entities(row['title'])
            description = clean_html_entities(row['description'])
            category = row['category']
            department = row['department']
            price = row['priceIncVat']
            image_url = row['largeImageUrl']
            
            # Create Shopify product data
            shopify_product = {
                "Handle": create_handle(title),
                "Title": title,
                "Body (HTML)": f"<p><strong>Part Number:</strong> {part_number}</p><p>{description}</p>",
                "Vendor": "Garador",
                "Product Type": department,
                "Tags": f"Garador, Spare Parts, {department}, {category}, Part-{part_number}",
                "Published": "TRUE",
                "Option1 Name": "Title",
                "Option1 Value": "Default Title",
                "Variant SKU": part_number,
                "Variant Grams": "",  # Leave empty for auto-calculation
                "Variant Inventory Tracker": "",  # Don't track inventory
                "Variant Inventory Qty": "",
                "Variant Inventory Policy": "continue",  # Allow purchases when out of stock
                "Variant Fulfillment Service": "manual",
                "Variant Price": price,
                "Variant Compare At Price": "",
                "Variant Requires Shipping": "TRUE",
                "Variant Taxable": "TRUE",
                "Variant Barcode": "",
                "Image Src": image_url,
                "Image Position": "1",
                "Image Alt Text": title,
                "Gift Card": "FALSE",
                "SEO Title": f"{title} - Garador Part {part_number}",
                "SEO Description": f"Genuine Garador spare part {part_number}. {description[:150]}...",
                "Collection": map_category_to_collection(category)
            }
            
            converted_products.append(shopify_product)
    
    # Write Shopify CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
        writer = csv.DictWriter(outfile, fieldnames=shopify_headers)
        writer.writeheader()
        writer.writerows(converted_products)
    
    print(f"Conversion complete!")
    print(f"Input file: {input_file}")
    print(f"Output file: {output_file}")
    print(f"Products converted: {len(converted_products)}")
    print(f"Products skipped (already exist): {len(existing_skus)}")
    
    return len(converted_products)

if __name__ == "__main__":
    convert_garador_to_shopify_csv()
