#!/usr/bin/env python3
"""
Create final full Shopify CSV for remaining products
"""

import csv

def create_final_csv():
    print('Creating final Shopify import CSV...')
    
    # Products to skip (already imported)
    skip_skus = {'233', '7333', '228', '59', '60', '187', '160161'}
    
    # Read all products from Garador CSV
    all_products = []
    with open('File (4).csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_products.append(row)
    
    # Filter out already imported products
    products_to_import = []
    for product in all_products:
        if product['partNumber'] not in skip_skus:
            products_to_import.append(product)
    
    print(f'Total products in CSV: {len(all_products)}')
    print(f'Already imported: {len(skip_skus)}')
    print(f'Remaining to import: {len(products_to_import)}')
    
    # Shopify CSV headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Variant SKU', 'Variant Price', 
        'Variant Requires Shipping', 'Variant Taxable', 'Image Src', 'Image Alt Text',
        'SEO Title', 'SEO Description', 'Collection'
    ]
    
    # Create the CSV file
    with open('shopify_final_import.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        
        for i, product in enumerate(products_to_import, 1):
            # Clean HTML entities
            title = product['title']
            title = title.replace('&amp;', '&').replace('&#39;', "'").replace('&nbsp;', ' ')
            title = title.replace('&quot;', '"').replace('&lt;', '<').replace('&gt;', '>')
            
            description = product['description']
            description = description.replace('&amp;', '&').replace('&#39;', "'").replace('&nbsp;', ' ')
            description = description.replace('&quot;', '"').replace('&lt;', '<').replace('&gt;', '>')
            
            # Create handle (URL-friendly)
            handle = title.lower()
            # Replace spaces and special characters
            replacements = {
                ' ': '-', '&': 'and', '(': '', ')': '', '/': '-', 
                ',': '', '"': '', "'": '', '--': '-', '---': '-'
            }
            for old, new in replacements.items():
                handle = handle.replace(old, new)
            
            # Clean up handle
            handle = handle.strip('-')[:100]  # Shopify limit
            
            # Create row
            shopify_row = {
                'Handle': handle,
                'Title': title,
                'Body (HTML)': f'<p><strong>Part Number:</strong> {product["partNumber"]}</p><p>{description}</p>',
                'Vendor': 'Garador',
                'Product Type': product['department'],
                'Tags': f'Garador, Spare Parts, {product["category"]}, Part-{product["partNumber"]}',
                'Published': 'TRUE',
                'Option1 Name': 'Title',
                'Option1 Value': 'Default Title',
                'Variant SKU': product['partNumber'],
                'Variant Price': product['priceIncVat'],
                'Variant Requires Shipping': 'TRUE',
                'Variant Taxable': 'TRUE',
                'Image Src': product['largeImageUrl'],
                'Image Alt Text': title,
                'SEO Title': f'{title} - Garador Part {product["partNumber"]}',
                'SEO Description': f'Genuine Garador spare part {product["partNumber"]}. {description[:120]}...',
                'Collection': product['category']
            }
            writer.writerow(shopify_row)
            
            # Progress indicator
            if i % 50 == 0:
                print(f'Processed {i}/{len(products_to_import)} products...')
    
    print(f'\\nFinal CSV created: shopify_final_import.csv')
    print(f'Products ready for import: {len(products_to_import)}')
    
    # Show breakdown by category
    category_counts = {}
    for product in products_to_import:
        cat = product['category']
        category_counts[cat] = category_counts.get(cat, 0) + 1
    
    print(f'\\nBreakdown by category:')
    for cat, count in sorted(category_counts.items()):
        print(f'  {cat}: {count} products')
    
    return len(products_to_import)

if __name__ == "__main__":
    create_final_csv()
