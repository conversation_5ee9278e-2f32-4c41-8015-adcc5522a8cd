#!/usr/bin/env python3
"""
Updated Garador Import Strategy
Now that Shopify MCP is working and SKUs are set correctly
"""

import csv
import json
from typing import Dict, List, Set

def load_csv_products(csv_file="File (4).csv") -> List[Dict]:
    """Load all products from CSV"""
    products = []
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            products.append(row)
    return products

def get_existing_skus_from_shopify() -> Set[str]:
    """
    Get existing SKUs from Shopify products
    This would use the get-products_shopify MCP tool
    """
    # Based on what we know, we have products with SKUs 233 and 7333
    existing_skus = {"233", "7333"}
    
    # In a real implementation, you'd call:
    # products = get_products_shopify(limit=250)  # Get all products
    # for product in products:
    #     for variant in product.variants:
    #         if variant.sku:
    #             existing_skus.add(variant.sku)
    
    return existing_skus

def find_products_to_import(csv_products: List[Dict], existing_skus: Set[str]) -> List[Dict]:
    """Find products that need to be imported"""
    to_import = []
    skipped = []
    
    for product in csv_products:
        sku = product['partNumber']
        if sku in existing_skus:
            skipped.append(sku)
        else:
            to_import.append(product)
    
    print(f"Products to import: {len(to_import)}")
    print(f"Products already exist (skipped): {len(skipped)}")
    print(f"Existing SKUs: {sorted(skipped)}")
    
    return to_import

def create_shopify_product_data(csv_row: Dict) -> Dict:
    """Convert CSV row to Shopify product format"""
    part_number = csv_row['partNumber']
    
    # Clean HTML entities
    title = csv_row['title'].replace('&amp;', '&').replace('&#39;', "'").replace('&nbsp;', ' ')
    description = csv_row['description'].replace('&amp;', '&').replace('&#39;', "'").replace('&nbsp;', ' ')
    
    product_data = {
        "title": title,
        "descriptionHtml": f"<p><strong>Part Number:</strong> {part_number}</p><p>{description}</p>",
        "vendor": "Garador",
        "productType": csv_row['department'],
        "status": "ACTIVE",
        "variants": [{
            "price": str(float(csv_row['priceIncVat'])),
            "sku": part_number,
            "options": ["Default Title"]
        }],
        "images": [{
            "src": csv_row['largeImageUrl'],
            "altText": title
        }],
        "tags": [
            "Garador",
            "Spare Parts", 
            csv_row['department'],
            csv_row['category'],
            f"Part-{part_number}"
        ],
        "seo": {
            "title": f"{title} - Garador Part {part_number}",
            "description": f"Genuine Garador spare part {part_number}. {description[:150]}..."
        }
    }
    
    return product_data

def generate_import_batches(products_to_import: List[Dict], batch_size: int = 10) -> List[List[Dict]]:
    """Split products into manageable batches"""
    batches = []
    for i in range(0, len(products_to_import), batch_size):
        batch = products_to_import[i:i + batch_size]
        batches.append(batch)
    return batches

def create_import_summary(csv_products: List[Dict], existing_skus: Set[str]):
    """Create a summary of the import plan"""
    products_to_import = find_products_to_import(csv_products, existing_skus)
    
    # Category breakdown
    category_counts = {}
    for product in products_to_import:
        category = product['category']
        category_counts[category] = category_counts.get(category, 0) + 1
    
    print("\n" + "="*60)
    print("GARADOR IMPORT SUMMARY")
    print("="*60)
    print(f"Total products in CSV: {len(csv_products)}")
    print(f"Already imported: {len(existing_skus)}")
    print(f"Ready to import: {len(products_to_import)}")
    print("\nProducts by category:")
    for category, count in sorted(category_counts.items()):
        print(f"  {category}: {count} products")
    
    print(f"\nImport batches (10 products each): {len(generate_import_batches(products_to_import))}")
    print("="*60)
    
    return products_to_import

def main():
    """Main import planning function"""
    print("Loading Garador products from CSV...")
    csv_products = load_csv_products()
    
    print("Checking existing products in Shopify...")
    existing_skus = get_existing_skus_from_shopify()
    
    print("Creating import plan...")
    products_to_import = create_import_summary(csv_products, existing_skus)
    
    # Create sample product data for first 3 products
    print("\nSample product data for first 3 products:")
    for i, product in enumerate(products_to_import[:3]):
        shopify_data = create_shopify_product_data(product)
        print(f"\n{i+1}. {shopify_data['title']}")
        print(f"   SKU: {shopify_data['variants'][0]['sku']}")
        print(f"   Price: £{shopify_data['variants'][0]['price']}")
        print(f"   Category: {product['category']}")
    
    return products_to_import

if __name__ == "__main__":
    products_to_import = main()
    
    print(f"\nNext steps:")
    print(f"1. Update collections as needed")
    print(f"2. Import products in batches of 10")
    print(f"3. Monitor for duplicates using SKU matching")
    print(f"4. Verify all {len(products_to_import)} products import correctly")
