#!/usr/bin/env python3
"""
Shopify Import Helper - Processes CSV data for Garador products
This script prepares data and provides functions to work with the Shopify MCP
"""

import csv
import json
import re
from typing import Dict, List, Set, Optional, Tuple

def load_csv_products(csv_file: str = "File (4).csv") -> List[Dict]:
    """Load all products from CSV file"""
    products = []
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            products.append(row)
    return products

def extract_part_number_from_description(description: str) -> Optional[str]:
    """Extract part number from Shopify product description"""
    match = re.search(r'Part number:\s*(\w+)', description)
    return match.group(1) if match else None

def get_unique_categories(products: List[Dict]) -> Dict[str, List[str]]:
    """Get unique departments and categories from CSV"""
    departments = set()
    categories = set()
    
    for product in products:
        departments.add(product['department'])
        categories.add(product['category'])
    
    return {
        "departments": sorted(list(departments)),
        "categories": sorted(list(categories))
    }

def create_collection_data(category: str, department: str) -> Dict:
    """Create collection data for Shopify"""
    descriptions = {
        "Canopy Doors": "Genuine Garador spare parts for canopy up & over garage doors including handles, springs, cables, and weatherstrips.",
        "Retractable Doors": "Spare parts for Garador retractable garage doors including tracks, springs, latches, and link arms.",
        "Retractable Plus Doors": "Components for Garador Retractable Plus garage doors with enhanced weather sealing.",
        "Double Doors": "Specialized parts for Garador double garage doors including tracks, springs, and hardware.",
        "Sectional Doors": "Spare parts for Garador sectional garage doors including panels, tracks, and springs.",
        "Side Doors": "Components for Garador side-hinged garage doors.",
        "Roller Doors": "Parts for Garador roller garage doors.",
        "GaraMatic Operators": "Electric garage door operators and accessories from Garador's GaraMatic range.",
        "EuroPro": "EuroPro electric operator components and accessories.",
        "Batteries": "Replacement batteries for Garador garage door operators and remote controls.",
        "Bulbs": "Replacement bulbs for Garador garage door operators and lighting systems.",
        "Touch Up Paint": "Touch-up paint in various colors for maintaining Garador garage doors."
    }
    
    return {
        "title": category,
        "description": descriptions.get(category, f"Garador {category} spare parts and components"),
        "handle": category.lower().replace(' ', '-').replace('&', 'and'),
        "seo": {
            "title": f"Garador {category} - Genuine Spare Parts",
            "description": descriptions.get(category, f"Shop genuine Garador {category} spare parts")
        },
        "templateSuffix": "collection-garador"
    }

def create_product_data(csv_row: Dict, collection_ids: Dict[str, str] = None) -> Dict:
    """Convert CSV row to Shopify product data"""
    part_number = csv_row['partNumber']
    category = csv_row['category']
    
    # Clean up description - remove HTML entities
    description = csv_row['description'].replace('&amp;', '&').replace('&nbsp;', ' ').replace('&#39;', "'").replace('&quot;', '"')
    
    product_data = {
        "title": csv_row['title'],
        "descriptionHtml": f"<p><strong>Part Number:</strong> {part_number}</p><p>{description}</p>",
        "vendor": "Garador",
        "productType": csv_row['department'],
        "status": "ACTIVE",
        "variants": [{
            "price": str(float(csv_row['priceIncVat'])),
            "sku": part_number,
            "inventoryManagement": "NOT_MANAGED",
            "inventoryPolicy": "CONTINUE",
            "taxable": True,
            "requiresShipping": True,
            "options": ["Default Title"]
        }],
        "images": [{
            "src": csv_row['largeImageUrl'],
            "altText": csv_row['title']
        }],
        "tags": [
            "Garador",
            "Spare Parts", 
            csv_row['department'],
            csv_row['category'],
            f"Part-{part_number}"
        ],
        "seo": {
            "title": f"{csv_row['title']} - Garador Part {part_number}",
            "description": f"Genuine Garador spare part {part_number}. {description[:150]}..."
        }
    }
    
    # Add to collection if collection_ids provided
    if collection_ids and category in collection_ids:
        product_data["collectionsToJoin"] = [collection_ids[category]]
    
    return product_data

def find_products_to_import(csv_products: List[Dict], existing_part_numbers: Set[str]) -> Tuple[List[Dict], List[str]]:
    """Find products that need to be imported (not already in Shopify)"""
    to_import = []
    skipped = []
    
    for product in csv_products:
        part_number = product['partNumber']
        if part_number in existing_part_numbers:
            skipped.append(part_number)
        else:
            to_import.append(product)
    
    return to_import, skipped

def batch_products(products: List[Dict], batch_size: int = 20) -> List[List[Dict]]:
    """Split products into batches for processing"""
    batches = []
    for i in range(0, len(products), batch_size):
        batches.append(products[i:i + batch_size])
    return batches

def validate_csv_data(csv_file: str = "File (4).csv") -> Dict:
    """Validate CSV data and return summary"""
    products = load_csv_products(csv_file)
    
    validation_results = {
        "total_products": len(products),
        "missing_fields": [],
        "duplicate_part_numbers": [],
        "invalid_prices": [],
        "missing_images": [],
        "categories": get_unique_categories(products)
    }
    
    part_numbers_seen = set()
    required_fields = ['partNumber', 'title', 'description', 'priceIncVat', 'largeImageUrl']
    
    for i, product in enumerate(products, 1):
        # Check required fields
        for field in required_fields:
            if not product.get(field) or product[field].strip() == '':
                validation_results["missing_fields"].append(f"Row {i}: Missing {field}")
        
        # Check for duplicate part numbers
        part_num = product.get('partNumber', '').strip()
        if part_num in part_numbers_seen:
            validation_results["duplicate_part_numbers"].append(part_num)
        part_numbers_seen.add(part_num)
        
        # Validate price
        try:
            price = float(product.get('priceIncVat', 0))
            if price <= 0:
                validation_results["invalid_prices"].append(f"Row {i}: Invalid price {price}")
        except (ValueError, TypeError):
            validation_results["invalid_prices"].append(f"Row {i}: Non-numeric price")
        
        # Check image URL
        image_url = product.get('largeImageUrl', '').strip()
        if not image_url or not image_url.startswith('http'):
            validation_results["missing_images"].append(f"Row {i}: Invalid image URL")
    
    return validation_results

if __name__ == "__main__":
    # Run validation
    print("Validating CSV data...")
    results = validate_csv_data()
    
    print(f"\nValidation Results:")
    print(f"Total products: {results['total_products']}")
    print(f"Departments: {len(results['categories']['departments'])}")
    print(f"Categories: {len(results['categories']['categories'])}")
    print(f"Missing fields: {len(results['missing_fields'])}")
    print(f"Duplicate part numbers: {len(results['duplicate_part_numbers'])}")
    print(f"Invalid prices: {len(results['invalid_prices'])}")
    print(f"Missing images: {len(results['missing_images'])}")
    
    if results['missing_fields']:
        print(f"\nFirst 5 missing fields: {results['missing_fields'][:5]}")
    
    print(f"\nCategories to create:")
    for category in results['categories']['categories']:
        print(f"  - {category}")
